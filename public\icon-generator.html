<!DOCTYPE html>
<html>
<head>
    <title>PWA Icon Generator</title>
</head>
<body>
    <canvas id="canvas192" width="192" height="192" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvas512" width="512" height="512" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    
    <script>
        // Generate 192x192 icon
        const canvas192 = document.getElementById('canvas192');
        const ctx192 = canvas192.getContext('2d');
        
        // Background
        ctx192.fillStyle = '#2563eb';
        ctx192.fillRect(0, 0, 192, 192);
        
        // Shopping cart icon
        ctx192.strokeStyle = '#ffffff';
        ctx192.fillStyle = '#ffffff';
        ctx192.lineWidth = 8;
        
        // Cart body
        ctx192.beginPath();
        ctx192.rect(60, 80, 80, 60);
        ctx192.stroke();
        
        // Cart handle
        ctx192.beginPath();
        ctx192.arc(50, 70, 15, 0, Math.PI, true);
        ctx192.stroke();
        
        // Cart wheels
        ctx192.beginPath();
        ctx192.arc(80, 160, 8, 0, 2 * Math.PI);
        ctx192.fill();
        
        ctx192.beginPath();
        ctx192.arc(120, 160, 8, 0, 2 * Math.PI);
        ctx192.fill();
        
        // Generate 512x512 icon
        const canvas512 = document.getElementById('canvas512');
        const ctx512 = canvas512.getContext('2d');
        
        // Background
        ctx512.fillStyle = '#2563eb';
        ctx512.fillRect(0, 0, 512, 512);
        
        // Shopping cart icon (scaled up)
        ctx512.strokeStyle = '#ffffff';
        ctx512.fillStyle = '#ffffff';
        ctx512.lineWidth = 20;
        
        // Cart body
        ctx512.beginPath();
        ctx512.rect(160, 220, 200, 160);
        ctx512.stroke();
        
        // Cart handle
        ctx512.beginPath();
        ctx512.arc(130, 190, 40, 0, Math.PI, true);
        ctx512.stroke();
        
        // Cart wheels
        ctx512.beginPath();
        ctx512.arc(210, 420, 20, 0, 2 * Math.PI);
        ctx512.fill();
        
        ctx512.beginPath();
        ctx512.arc(310, 420, 20, 0, 2 * Math.PI);
        ctx512.fill();
        
        // Download function
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Auto download
        setTimeout(() => {
            downloadCanvas(canvas192, 'pwa-192x192.png');
            downloadCanvas(canvas512, 'pwa-512x512.png');
        }, 1000);
    </script>
</body>
</html>
