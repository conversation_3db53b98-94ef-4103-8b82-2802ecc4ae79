import React, { useState, useEffect } from 'react';

const ProductForm = ({ 
  product = null, 
  categories = [], 
  onSubmit, 
  onCancel, 
  loading = false 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    barcode: '',
    category: '',
    unit: 'حبة',
    purchasePrice: '',
    salePrice: '',
    stock: '',
    minStock: '',
    description: '',
    supplier: '',
    location: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Common units for products
  const commonUnits = [
    'حبة', 'كيلو', 'جرام', 'لتر', 'مل', 'كرتون', 'علبة', 'كيس', 'زجاجة', 'عبوة'
  ];

  // Initialize form with product data if editing
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || '',
        barcode: product.barcode || '',
        category: product.category || '',
        unit: product.unit || 'حبة',
        purchasePrice: product.purchasePrice?.toString() || '',
        salePrice: product.salePrice?.toString() || '',
        stock: product.stock?.toString() || '',
        minStock: product.minStock?.toString() || '',
        description: product.description || '',
        supplier: product.supplier || '',
        location: product.location || ''
      });
    }
  }, [product]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields
    if (!formData.name.trim()) {
      newErrors.name = 'اسم المنتج مطلوب';
    }

    if (!formData.salePrice || parseFloat(formData.salePrice) <= 0) {
      newErrors.salePrice = 'سعر البيع مطلوب ويجب أن يكون أكبر من صفر';
    }

    // Validate numbers
    if (formData.purchasePrice && parseFloat(formData.purchasePrice) < 0) {
      newErrors.purchasePrice = 'سعر الشراء لا يمكن أن يكون سالباً';
    }

    if (formData.stock && parseInt(formData.stock) < 0) {
      newErrors.stock = 'الكمية لا يمكن أن تكون سالبة';
    }

    if (formData.minStock && parseInt(formData.minStock) < 0) {
      newErrors.minStock = 'الحد الأدنى للمخزون لا يمكن أن يكون سالباً';
    }

    // Validate profit margin
    const purchasePrice = parseFloat(formData.purchasePrice) || 0;
    const salePrice = parseFloat(formData.salePrice) || 0;
    if (purchasePrice > 0 && salePrice > 0 && salePrice <= purchasePrice) {
      newErrors.salePrice = 'سعر البيع يجب أن يكون أكبر من سعر الشراء';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateProfitMargin = () => {
    const purchasePrice = parseFloat(formData.purchasePrice) || 0;
    const salePrice = parseFloat(formData.salePrice) || 0;
    
    if (purchasePrice > 0 && salePrice > 0) {
      const margin = ((salePrice - purchasePrice) / purchasePrice) * 100;
      return margin.toFixed(1);
    }
    return '0';
  };

  const calculateProfit = () => {
    const purchasePrice = parseFloat(formData.purchasePrice) || 0;
    const salePrice = parseFloat(formData.salePrice) || 0;
    return salePrice - purchasePrice;
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-xl font-semibold">
          {product ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        </h3>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Product Name */}
          <div className="form-group">
            <label className="form-label">
              اسم المنتج <span className="text-danger">*</span>
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={`form-input ${errors.name ? 'border-danger' : ''}`}
              placeholder="أدخل اسم المنتج"
              required
            />
            {errors.name && <p className="text-danger text-sm mt-1">{errors.name}</p>}
          </div>

          {/* Barcode */}
          <div className="form-group">
            <label className="form-label">الباركود</label>
            <input
              type="text"
              name="barcode"
              value={formData.barcode}
              onChange={handleInputChange}
              className="form-input"
              placeholder="أدخل الباركود (اختياري)"
            />
          </div>

          {/* Category */}
          <div className="form-group">
            <label className="form-label">الفئة</label>
            <input
              type="text"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="form-input"
              placeholder="أدخل فئة المنتج"
              list="categories"
            />
            <datalist id="categories">
              {categories.map((category, index) => (
                <option key={index} value={category} />
              ))}
            </datalist>
          </div>

          {/* Unit */}
          <div className="form-group">
            <label className="form-label">الوحدة</label>
            <select
              name="unit"
              value={formData.unit}
              onChange={handleInputChange}
              className="form-select"
            >
              {commonUnits.map((unit, index) => (
                <option key={index} value={unit}>{unit}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Pricing */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Purchase Price */}
          <div className="form-group">
            <label className="form-label">سعر الشراء</label>
            <input
              type="number"
              name="purchasePrice"
              value={formData.purchasePrice}
              onChange={handleInputChange}
              className={`form-input ${errors.purchasePrice ? 'border-danger' : ''}`}
              placeholder="0.00"
              step="0.01"
              min="0"
            />
            {errors.purchasePrice && <p className="text-danger text-sm mt-1">{errors.purchasePrice}</p>}
          </div>

          {/* Sale Price */}
          <div className="form-group">
            <label className="form-label">
              سعر البيع <span className="text-danger">*</span>
            </label>
            <input
              type="number"
              name="salePrice"
              value={formData.salePrice}
              onChange={handleInputChange}
              className={`form-input ${errors.salePrice ? 'border-danger' : ''}`}
              placeholder="0.00"
              step="0.01"
              min="0.01"
              required
            />
            {errors.salePrice && <p className="text-danger text-sm mt-1">{errors.salePrice}</p>}
          </div>

          {/* Profit Info */}
          <div className="form-group">
            <label className="form-label">الربح</label>
            <div className="bg-gray-50 p-3 rounded border">
              <div className="text-sm">
                <div className="flex justify-between">
                  <span>الربح:</span>
                  <span className="font-medium">{calculateProfit().toFixed(2)} ريال</span>
                </div>
                <div className="flex justify-between">
                  <span>هامش الربح:</span>
                  <span className="font-medium">{calculateProfitMargin()}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stock Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Current Stock */}
          <div className="form-group">
            <label className="form-label">الكمية الحالية</label>
            <input
              type="number"
              name="stock"
              value={formData.stock}
              onChange={handleInputChange}
              className={`form-input ${errors.stock ? 'border-danger' : ''}`}
              placeholder="0"
              min="0"
            />
            {errors.stock && <p className="text-danger text-sm mt-1">{errors.stock}</p>}
          </div>

          {/* Minimum Stock */}
          <div className="form-group">
            <label className="form-label">الحد الأدنى للمخزون</label>
            <input
              type="number"
              name="minStock"
              value={formData.minStock}
              onChange={handleInputChange}
              className={`form-input ${errors.minStock ? 'border-danger' : ''}`}
              placeholder="10"
              min="0"
            />
            {errors.minStock && <p className="text-danger text-sm mt-1">{errors.minStock}</p>}
          </div>
        </div>

        {/* Additional Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Supplier */}
          <div className="form-group">
            <label className="form-label">المورد</label>
            <input
              type="text"
              name="supplier"
              value={formData.supplier}
              onChange={handleInputChange}
              className="form-input"
              placeholder="اسم المورد"
            />
          </div>

          {/* Location */}
          <div className="form-group">
            <label className="form-label">الموقع في المخزن</label>
            <input
              type="text"
              name="location"
              value={formData.location}
              onChange={handleInputChange}
              className="form-input"
              placeholder="مثال: رف A - مستوى 2"
            />
          </div>
        </div>

        {/* Description */}
        <div className="form-group">
          <label className="form-label">الوصف</label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            className="form-input"
            rows="3"
            placeholder="وصف إضافي للمنتج (اختياري)"
          />
        </div>

        {/* Form Actions */}
        <div className="flex gap-4 pt-4">
          <button
            type="submit"
            disabled={isSubmitting || loading}
            className="btn btn-primary flex-1"
          >
            {isSubmitting ? (
              <>
                <div className="spinner mr-2"></div>
                جاري الحفظ...
              </>
            ) : (
              product ? 'تحديث المنتج' : 'إضافة المنتج'
            )}
          </button>
          
          <button
            type="button"
            onClick={onCancel}
            disabled={isSubmitting}
            className="btn btn-secondary"
          >
            إلغاء
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProductForm;
