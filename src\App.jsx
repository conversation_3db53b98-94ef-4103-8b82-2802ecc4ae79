import React, { useState } from 'react';
import Navigation from './components/Navigation';
import Welcome from './components/Welcome';
import ProductManagement from './components/ProductManagement';
import './styles/global.css';

function App() {
  const [currentPage, setCurrentPage] = useState('welcome');

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'welcome':
        return <Welcome onNavigate={setCurrentPage} />;
      case 'products':
        return <ProductManagement />;
      case 'sales':
        return (
          <div className="container py-8">
            <div className="card text-center">
              <h2 className="text-2xl font-bold mb-4">شاشة المبيعات</h2>
              <p className="text-gray-600">ستكون متاحة في المرحلة الثالثة</p>
            </div>
          </div>
        );
      case 'purchases':
        return (
          <div className="container py-8">
            <div className="card text-center">
              <h2 className="text-2xl font-bold mb-4">شاشة المشتريات</h2>
              <p className="text-gray-600">ستكون متاحة في المرحلة الخامسة</p>
            </div>
          </div>
        );
      case 'customers':
        return (
          <div className="container py-8">
            <div className="card text-center">
              <h2 className="text-2xl font-bold mb-4">إدارة العملاء</h2>
              <p className="text-gray-600">ستكون متاحة في المرحلة الرابعة</p>
            </div>
          </div>
        );
      case 'reports':
        return (
          <div className="container py-8">
            <div className="card text-center">
              <h2 className="text-2xl font-bold mb-4">التقارير</h2>
              <p className="text-gray-600">ستكون متاحة في المرحلة السادسة</p>
            </div>
          </div>
        );
      case 'settings':
        return (
          <div className="container py-8">
            <div className="card text-center">
              <h2 className="text-2xl font-bold mb-4">الإعدادات</h2>
              <p className="text-gray-600">ستكون متاحة في المرحلة السابعة</p>
            </div>
          </div>
        );
      default:
        return <Welcome onNavigate={setCurrentPage} />;
    }
  };

  return (
    <div className="App min-h-screen bg-light-bg">
      <Navigation currentPage={currentPage} onPageChange={setCurrentPage} />
      {renderCurrentPage()}
    </div>
  );
}

export default App;
