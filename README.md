# نظام إدارة المبيعات والمشتريات - المرحلة 1

تطبيق ويب PWA لإدارة المبيعات والمشتريات في المحلات التجارية، يعمل بدون إنترنت ويدعم اللغة العربية مع اتجاه RTL.

## المرحلة الحالية: إدارة الأصناف والمخزون ✅

### الميزات المكتملة:

#### المرحلة 1: تهيئة المشروع ✅
- ✅ إعداد مشروع React مع Vite
- ✅ تكوين PWA مع Service Worker
- ✅ إعداد IndexedDB مع جداول البيانات الأساسية
- ✅ واجهة RTL عربية مع خطوط Google Arabic
- ✅ شاشة ترحيب تفاعلية
- ✅ نظام تثبيت PWA على الأجهزة

#### المرحلة 2: إدارة الأصناف والمخزون ✅
- ✅ نظام تنقل متقدم مع شريط علوي
- ✅ إدارة شاملة للمنتجات (إضافة، تعديل، حذف)
- ✅ نموذج منتجات متطور مع التحقق من البيانات
- ✅ قائمة منتجات تفاعلية مع بحث وفلترة
- ✅ نظام تنبيهات المخزون المنخفض والمنتهي
- ✅ لوحة تحكم مع إحصائيات فورية
- ✅ حساب قيمة المخزون والأرباح المتوقعة
- ✅ دعم الفئات والوحدات المختلفة

### جداول قاعدة البيانات:

#### 1. Products (المنتجات)
```javascript
{
  id: number (auto-increment),
  name: string,
  barcode: string,
  category: string,
  unit: string,
  purchasePrice: number,
  salePrice: number,
  stock: number,
  minStock: number,
  createdAt: string,
  updatedAt: string
}
```

#### 2. Customers (العملاء)
```javascript
{
  id: number (auto-increment),
  name: string,
  phone: string,
  address: string,
  totalDebt: number,
  createdAt: string,
  updatedAt: string
}
```

#### 3. Suppliers (الموردين)
```javascript
{
  id: number (auto-increment),
  name: string,
  phone: string,
  address: string,
  createdAt: string,
  updatedAt: string
}
```

#### 4. Transactions (المعاملات)
```javascript
{
  id: number (auto-increment),
  type: string, // 'sale' | 'purchase' | 'return'
  customerId: number,
  supplierId: number,
  items: array,
  total: number,
  paid: number,
  change: number,
  paymentMethod: string,
  date: string,
  createdAt: string,
  updatedAt: string
}
```

#### 5. Debts (الديون)
```javascript
{
  id: number (auto-increment),
  customerId: number,
  amount: number,
  description: string,
  dueDate: string,
  status: string, // 'pending' | 'paid' | 'overdue'
  createdAt: string,
  updatedAt: string
}
```

#### 6. Settings (الإعدادات)
```javascript
{
  key: string,
  value: any
}
```

## التشغيل

### التطوير
```bash
npm install
npm run dev
```

### البناء للإنتاج
```bash
npm run build
npm run preview
```

## الميزات التقنية

### PWA (Progressive Web App)
- يعمل بدون إنترنت
- قابل للتثبيت على الأجهزة
- تحديث تلقائي للمحتوى
- تخزين مؤقت ذكي

### قاعدة البيانات
- IndexedDB للتخزين المحلي
- عمليات CRUD كاملة
- فهرسة للبحث السريع
- نسخ احتياطي وإستيراد/تصدير

### واجهة المستخدم
- تصميم RTL للغة العربية
- خطوط Google Arabic (Cairo & Tajawal)
- تصميم متجاوب لجميع الأجهزة
- أزرار كبيرة صديقة للمس

## المراحل القادمة

### المرحلة 3: شاشة المبيعات الفورية
- واجهة بيع سريعة
- دعم الباركود
- طباعة الفواتير
- حساب الضرائب والخصومات

### المرحلة 4: البيع بالآجل وإدارة الديون
- نظام الديون والأرصدة
- تتبع مواعيد الاستحقاق
- تسديد الديون
- إشعارات التذكير

### المرحلة 5: المشتريات من الموردين
- شاشة المشتريات
- إدارة الموردين
- تحديث المخزون
- تتبع التكاليف

### المرحلة 6: التقارير ولوحة المعلومات
- تقارير المبيعات والمشتريات
- إحصائيات الأرباح
- تقارير المخزون
- تصدير PDF/Excel

### المرحلة 7: الإعدادات والاختبارات
- إعدادات المتجر
- النسخ الاحتياطي
- اختبارات الوحدة
- تحديثات قاعدة البيانات

## التقنيات المستخدمة

- **Frontend**: React 18 + Vite
- **Database**: IndexedDB مع مكتبة idb
- **PWA**: vite-plugin-pwa + Workbox
- **Styling**: CSS3 مخصص مع دعم RTL
- **Fonts**: Google Fonts (Cairo & Tajawal)

## الدعم والمتطلبات

- متصفحات حديثة تدعم IndexedDB
- دعم Service Workers للعمل بدون إنترنت
- أجهزة Android/iOS للتثبيت كتطبيق
- ذاكرة تخزين محلية كافية

---

**تم تطوير المرحلة 1 بنجاح ✅**

الخطوة التالية: تشغيل `npm run dev` لاختبار التطبيق والانتقال للمرحلة 2.
