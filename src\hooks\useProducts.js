import { useState, useEffect, useCallback } from 'react';
import { productsDB, getSettings } from '../utils/db';

export const useProducts = () => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lowStockThreshold, setLowStockThreshold] = useState(10);

  // Load products from database
  const loadProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [productsData, settings] = await Promise.all([
        productsDB.getAll(),
        getSettings()
      ]);
      
      setProducts(productsData);
      setLowStockThreshold(settings.lowStockThreshold || 10);
      
      // Extract unique categories
      const uniqueCategories = [...new Set(productsData.map(p => p.category).filter(Boolean))];
      setCategories(uniqueCategories);
      
    } catch (err) {
      console.error('Error loading products:', err);
      setError('خطأ في تحميل المنتجات');
    } finally {
      setLoading(false);
    }
  }, []);

  // Add new product
  const addProduct = useCallback(async (productData) => {
    try {
      setError(null);
      
      // Validate required fields
      if (!productData.name || !productData.salePrice) {
        throw new Error('اسم المنتج وسعر البيع مطلوبان');
      }
      
      // Check if barcode already exists (if provided)
      if (productData.barcode) {
        const existingProducts = await productsDB.getByIndex('barcode', productData.barcode);
        if (existingProducts.length > 0) {
          throw new Error('الباركود موجود مسبقاً');
        }
      }
      
      const newProduct = {
        name: productData.name.trim(),
        barcode: productData.barcode?.trim() || '',
        category: productData.category?.trim() || 'عام',
        unit: productData.unit?.trim() || 'حبة',
        purchasePrice: parseFloat(productData.purchasePrice) || 0,
        salePrice: parseFloat(productData.salePrice),
        stock: parseInt(productData.stock) || 0,
        minStock: parseInt(productData.minStock) || lowStockThreshold,
        description: productData.description?.trim() || '',
        supplier: productData.supplier?.trim() || '',
        location: productData.location?.trim() || ''
      };
      
      const id = await productsDB.add(newProduct);
      await loadProducts(); // Reload to get updated data
      
      return { success: true, id };
    } catch (err) {
      console.error('Error adding product:', err);
      const errorMessage = err.message || 'خطأ في إضافة المنتج';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [loadProducts, lowStockThreshold]);

  // Update existing product
  const updateProduct = useCallback(async (id, productData) => {
    try {
      setError(null);
      
      // Validate required fields
      if (!productData.name || !productData.salePrice) {
        throw new Error('اسم المنتج وسعر البيع مطلوبان');
      }
      
      // Check if barcode already exists for other products (if provided)
      if (productData.barcode) {
        const existingProducts = await productsDB.getByIndex('barcode', productData.barcode);
        const conflictingProduct = existingProducts.find(p => p.id !== id);
        if (conflictingProduct) {
          throw new Error('الباركود موجود مسبقاً لمنتج آخر');
        }
      }
      
      const updatedData = {
        name: productData.name.trim(),
        barcode: productData.barcode?.trim() || '',
        category: productData.category?.trim() || 'عام',
        unit: productData.unit?.trim() || 'حبة',
        purchasePrice: parseFloat(productData.purchasePrice) || 0,
        salePrice: parseFloat(productData.salePrice),
        stock: parseInt(productData.stock) || 0,
        minStock: parseInt(productData.minStock) || lowStockThreshold,
        description: productData.description?.trim() || '',
        supplier: productData.supplier?.trim() || '',
        location: productData.location?.trim() || ''
      };
      
      await productsDB.update(id, updatedData);
      await loadProducts(); // Reload to get updated data
      
      return { success: true };
    } catch (err) {
      console.error('Error updating product:', err);
      const errorMessage = err.message || 'خطأ في تحديث المنتج';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [loadProducts, lowStockThreshold]);

  // Delete product
  const deleteProduct = useCallback(async (id) => {
    try {
      setError(null);
      await productsDB.delete(id);
      await loadProducts(); // Reload to get updated data
      return { success: true };
    } catch (err) {
      console.error('Error deleting product:', err);
      const errorMessage = 'خطأ في حذف المنتج';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [loadProducts]);

  // Update stock quantity
  const updateStock = useCallback(async (id, newStock, reason = '') => {
    try {
      setError(null);
      const product = await productsDB.getById(id);
      if (!product) {
        throw new Error('المنتج غير موجود');
      }
      
      await productsDB.update(id, { 
        stock: parseInt(newStock),
        lastStockUpdate: new Date().toISOString(),
        stockUpdateReason: reason
      });
      
      await loadProducts(); // Reload to get updated data
      return { success: true };
    } catch (err) {
      console.error('Error updating stock:', err);
      const errorMessage = 'خطأ في تحديث المخزون';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [loadProducts]);

  // Search products
  const searchProducts = useCallback((query, filters = {}) => {
    if (!query && !Object.keys(filters).length) {
      return products;
    }
    
    return products.filter(product => {
      // Text search
      if (query) {
        const searchTerm = query.toLowerCase();
        const matchesName = product.name.toLowerCase().includes(searchTerm);
        const matchesBarcode = product.barcode.toLowerCase().includes(searchTerm);
        const matchesCategory = product.category.toLowerCase().includes(searchTerm);
        
        if (!matchesName && !matchesBarcode && !matchesCategory) {
          return false;
        }
      }
      
      // Category filter
      if (filters.category && product.category !== filters.category) {
        return false;
      }
      
      // Low stock filter
      if (filters.lowStock && product.stock > product.minStock) {
        return false;
      }
      
      // Out of stock filter
      if (filters.outOfStock && product.stock > 0) {
        return false;
      }
      
      return true;
    });
  }, [products]);

  // Get low stock products
  const getLowStockProducts = useCallback(() => {
    return products.filter(product => product.stock <= product.minStock && product.stock > 0);
  }, [products]);

  // Get out of stock products
  const getOutOfStockProducts = useCallback(() => {
    return products.filter(product => product.stock === 0);
  }, [products]);

  // Get products by category
  const getProductsByCategory = useCallback((category) => {
    return products.filter(product => product.category === category);
  }, [products]);

  // Calculate total inventory value
  const getTotalInventoryValue = useCallback(() => {
    return products.reduce((total, product) => {
      return total + (product.stock * product.purchasePrice);
    }, 0);
  }, [products]);

  // Calculate potential profit
  const getPotentialProfit = useCallback(() => {
    return products.reduce((total, product) => {
      const profit = (product.salePrice - product.purchasePrice) * product.stock;
      return total + (profit > 0 ? profit : 0);
    }, 0);
  }, [products]);

  // Load products on mount
  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  return {
    // State
    products,
    categories,
    loading,
    error,
    lowStockThreshold,
    
    // Actions
    loadProducts,
    addProduct,
    updateProduct,
    deleteProduct,
    updateStock,
    
    // Queries
    searchProducts,
    getLowStockProducts,
    getOutOfStockProducts,
    getProductsByCategory,
    getTotalInventoryValue,
    getPotentialProfit,
    
    // Clear error
    clearError: () => setError(null)
  };
};
