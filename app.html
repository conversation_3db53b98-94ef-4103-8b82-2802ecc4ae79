<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المبيعات والمشتريات</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .subtitle {
            color: #666;
            font-size: 1.2rem;
        }

        .nav-menu {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .nav-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .nav-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 20px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-btn.active {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        .nav-btn.disabled {
            background: #ccc;
            cursor: not-allowed;
            position: relative;
        }

        .nav-btn.disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .coming-soon {
            position: absolute;
            top: -5px;
            left: -5px;
            background: #ff6b6b;
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
        }

        .content-area {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            min-height: 400px;
        }

        .welcome-content {
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .feature-icon {
            width: 30px;
            height: 30px;
            background: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .hidden {
            display: none;
        }

        .products-content h2 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .product-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            margin-left: 10px;
        }

        .btn:hover {
            opacity: 0.9;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-danger {
            background: #dc3545;
        }

        .products-list {
            margin-top: 20px;
        }

        .product-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .product-info h4 {
            margin-bottom: 5px;
            color: #333;
        }

        .product-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .product-actions button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            margin-left: 5px;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border: 1px solid #c3e6cb;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .nav-buttons {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🏪</div>
            <h1>متجر المواد الغذائية</h1>
            <p class="subtitle">نظام إدارة المبيعات والمشتريات - المرحلة الثانية مكتملة ✅</p>
        </div>

        <!-- Navigation -->
        <div class="nav-menu">
            <div class="nav-buttons">
                <button class="nav-btn active" id="welcome-btn" onclick="showPage('welcome')">
                    🏠 الرئيسية
                </button>
                <button class="nav-btn" id="products-btn" onclick="showPage('products')">
                    📦 إدارة المنتجات
                </button>
                <button class="nav-btn disabled">
                    🛒 المبيعات
                    <span class="coming-soon">المرحلة 3</span>
                </button>
                <button class="nav-btn disabled">
                    👥 العملاء
                    <span class="coming-soon">المرحلة 4</span>
                </button>
                <button class="nav-btn disabled">
                    📊 التقارير
                    <span class="coming-soon">المرحلة 6</span>
                </button>
                <button class="nav-btn disabled">
                    ⚙️ الإعدادات
                    <span class="coming-soon">المرحلة 7</span>
                </button>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Welcome Page -->
            <div id="welcome-page" class="page-content">
                <div class="welcome-content">
                    <h2>🎉 مرحباً بك في نظام إدارة المبيعات والمشتريات</h2>
                    <p>تطبيق شامل لإدارة متجرك بكفاءة عالية - المرحلة الثانية مكتملة بنجاح!</p>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="total-products">0</div>
                            <div class="stat-label">إجمالي المنتجات</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="total-value">0</div>
                            <div class="stat-label">قيمة المخزون (ريال)</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">✅</div>
                            <div class="stat-label">المرحلة الثانية مكتملة</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">📱</div>
                            <div class="stat-label">تطبيق ويب متجاوب</div>
                        </div>
                    </div>

                    <h3>🏆 الميزات المكتملة في المرحلة الثانية:</h3>
                    <div class="features-list">
                        <div class="feature-item">
                            <div class="feature-icon">✓</div>
                            <span>واجهة عربية RTL جميلة ومتجاوبة</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">✓</div>
                            <span>إدارة شاملة للمنتجات (إضافة، عرض، حذف)</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">✓</div>
                            <span>تخزين محلي آمن للبيانات</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">✓</div>
                            <span>نظام تنقل سهل ومرن</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">✓</div>
                            <span>إحصائيات فورية ومعلومات المخزون</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">✓</div>
                            <span>تصميم متجاوب لجميع الأجهزة</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Page -->
            <div id="products-page" class="page-content hidden">
                <div class="products-content">
                    <h2>📦 إدارة المنتجات والمخزون</h2>
                    
                    <div id="success-message" class="success-message hidden">
                        تم حفظ المنتج بنجاح! ✅
                    </div>
                    
                    <div class="product-form">
                        <h3>إضافة منتج جديد</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label>اسم المنتج:</label>
                                <input type="text" id="product-name" placeholder="أدخل اسم المنتج">
                            </div>
                            <div class="form-group">
                                <label>الباركود:</label>
                                <input type="text" id="product-barcode" placeholder="أدخل الباركود">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>سعر الشراء (ريال):</label>
                                <input type="number" id="product-cost" placeholder="0.00" step="0.01">
                            </div>
                            <div class="form-group">
                                <label>سعر البيع (ريال):</label>
                                <input type="number" id="product-price" placeholder="0.00" step="0.01">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>الكمية:</label>
                                <input type="number" id="product-quantity" placeholder="0">
                            </div>
                            <div class="form-group">
                                <label>الفئة:</label>
                                <select id="product-category">
                                    <option value="">اختر الفئة</option>
                                    <option value="مواد غذائية">مواد غذائية</option>
                                    <option value="مشروبات">مشروبات</option>
                                    <option value="منظفات">منظفات</option>
                                    <option value="أدوات منزلية">أدوات منزلية</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <button class="btn" onclick="addProduct()">إضافة المنتج</button>
                        <button class="btn btn-secondary" onclick="clearForm()">مسح النموذج</button>
                    </div>

                    <div class="products-list">
                        <h3>📋 قائمة المنتجات</h3>
                        <div id="products-container">
                            <p style="text-align: center; color: #666; padding: 20px;">لا توجد منتجات حالياً. أضف منتجك الأول!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تخزين البيانات محلياً
        let products = JSON.parse(localStorage.getItem('products')) || [];

        // عرض الصفحات
        function showPage(pageId) {
            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // إضافة الفئة النشطة للزر المحدد
            document.getElementById(pageId + '-btn').classList.add('active');
            
            // إخفاء جميع الصفحات
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.add('hidden');
            });
            
            // عرض الصفحة المطلوبة
            document.getElementById(pageId + '-page').classList.remove('hidden');
            
            // تحديث الإحصائيات إذا كانت الصفحة الرئيسية
            if (pageId === 'welcome') {
                updateStats();
            }
            
            // تحديث قائمة المنتجات إذا كانت صفحة المنتجات
            if (pageId === 'products') {
                displayProducts();
            }
        }

        // إضافة منتج جديد
        function addProduct() {
            const name = document.getElementById('product-name').value.trim();
            const barcode = document.getElementById('product-barcode').value.trim();
            const cost = parseFloat(document.getElementById('product-cost').value) || 0;
            const price = parseFloat(document.getElementById('product-price').value) || 0;
            const quantity = parseInt(document.getElementById('product-quantity').value) || 0;
            const category = document.getElementById('product-category').value;

            if (!name) {
                alert('⚠️ يرجى إدخال اسم المنتج');
                return;
            }

            if (price <= 0) {
                alert('⚠️ يرجى إدخال سعر بيع صحيح');
                return;
            }

            const product = {
                id: Date.now(),
                name,
                barcode: barcode || 'غير محدد',
                cost,
                price,
                quantity,
                category: category || 'غير محدد',
                profit: price - cost,
                createdAt: new Date().toLocaleDateString('ar-SA')
            };

            products.push(product);
            localStorage.setItem('products', JSON.stringify(products));
            
            clearForm();
            displayProducts();
            showSuccessMessage();
        }

        // عرض رسالة النجاح
        function showSuccessMessage() {
            const message = document.getElementById('success-message');
            message.classList.remove('hidden');
            setTimeout(() => {
                message.classList.add('hidden');
            }, 3000);
        }

        // مسح النموذج
        function clearForm() {
            document.getElementById('product-name').value = '';
            document.getElementById('product-barcode').value = '';
            document.getElementById('product-cost').value = '';
            document.getElementById('product-price').value = '';
            document.getElementById('product-quantity').value = '';
            document.getElementById('product-category').value = '';
        }

        // عرض المنتجات
        function displayProducts() {
            const container = document.getElementById('products-container');
            
            if (products.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">لا توجد منتجات حالياً. أضف منتجك الأول!</p>';
                return;
            }

            container.innerHTML = products.map(product => `
                <div class="product-item">
                    <div class="product-info">
                        <h4>📦 ${product.name}</h4>
                        <p><strong>الباركود:</strong> ${product.barcode} | <strong>الفئة:</strong> ${product.category}</p>
                        <p><strong>الكمية:</strong> ${product.quantity} | <strong>سعر البيع:</strong> ${product.price} ريال | <strong>الربح:</strong> ${product.profit.toFixed(2)} ريال</p>
                        <p><strong>تاريخ الإضافة:</strong> ${product.createdAt}</p>
                    </div>
                    <div class="product-actions">
                        <button onclick="deleteProduct(${product.id})" class="btn-danger">🗑️ حذف</button>
                    </div>
                </div>
            `).join('');
        }

        // حذف منتج
        function deleteProduct(id) {
            if (confirm('🗑️ هل أنت متأكد من حذف هذا المنتج؟')) {
                products = products.filter(product => product.id !== id);
                localStorage.setItem('products', JSON.stringify(products));
                displayProducts();
                updateStats();
            }
        }

        // تحديث الإحصائيات
        function updateStats() {
            const totalProducts = products.length;
            const totalValue = products.reduce((sum, product) => sum + (product.price * product.quantity), 0);
            
            document.getElementById('total-products').textContent = totalProducts;
            document.getElementById('total-value').textContent = totalValue.toLocaleString();
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            showPage('welcome');
            updateStats();
        });
    </script>
</body>
</html>
