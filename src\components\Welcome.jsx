import React, { useState, useEffect } from 'react';
import { initDB, getSettings } from '../utils/db';

const Welcome = ({ onNavigate }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [dbStatus, setDbStatus] = useState('جاري التحميل...');
  const [settings, setSettings] = useState({});
  const [installPrompt, setInstallPrompt] = useState(null);
  const [showInstallButton, setShowInstallButton] = useState(false);

  useEffect(() => {
    initializeApp();

    // PWA install prompt
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault();
      setInstallPrompt(e);
      setShowInstallButton(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const initializeApp = async () => {
    try {
      setDbStatus('جاري تهيئة قاعدة البيانات...');
      await initDB();
      setDbStatus('تم تحميل قاعدة البيانات بنجاح');

      const appSettings = await getSettings();
      setSettings(appSettings);

      setTimeout(() => {
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error initializing app:', error);
      setDbStatus('خطأ في تحميل قاعدة البيانات');
      setIsLoading(false);
    }
  };

  const handleInstallApp = async () => {
    if (installPrompt) {
      const result = await installPrompt.prompt();
      console.log('Install prompt result:', result);
      setInstallPrompt(null);
      setShowInstallButton(false);
    }
  };

  const checkOfflineStatus = () => {
    return navigator.onLine ? 'متصل' : 'غير متصل';
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="card text-center max-w-md w-full mx-4">
          <div className="mb-6">
            <div className="w-16 h-16 bg-primary-color rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2">
              نظام إدارة المبيعات والمشتريات
            </h1>
            <p className="text-gray-600">
              تطبيق شامل لإدارة المحلات التجارية
            </p>
          </div>

          <div className="mb-6">
            <div className="spinner mx-auto mb-3"></div>
            <p className="text-sm text-gray-600">{dbStatus}</p>
          </div>

          <div className="text-xs text-gray-500">
            الحالة: {checkOfflineStatus()} • الإصدار 1.0.0
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-primary-color rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            {settings.storeName || 'متجر المواد الغذائية'}
          </h1>
          <p className="text-gray-600">
            نظام إدارة المبيعات والمشتريات
          </p>
        </div>

        {/* PWA Install Prompt */}
        {showInstallButton && (
          <div className="card mb-6 bg-blue-50 border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-blue-800 mb-1">
                  تثبيت التطبيق
                </h3>
                <p className="text-sm text-blue-600">
                  ثبت التطبيق على جهازك للوصول السريع والعمل بدون إنترنت
                </p>
              </div>
              <button
                onClick={handleInstallApp}
                className="btn btn-primary btn-sm"
              >
                تثبيت
              </button>
            </div>
          </div>
        )}

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="card text-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-800 mb-1">قاعدة البيانات</h3>
            <p className="text-sm text-green-600">جاهزة للاستخدام</p>
          </div>

          <div className="card text-center">
            <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 ${
              navigator.onLine ? 'bg-green-100' : 'bg-orange-100'
            }`}>
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {navigator.onLine ? (
                  <path className="text-green-600" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                ) : (
                  <path className="text-orange-600" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728" />
                )}
              </svg>
            </div>
            <h3 className="font-semibold text-gray-800 mb-1">حالة الاتصال</h3>
            <p className={`text-sm ${navigator.onLine ? 'text-green-600' : 'text-orange-600'}`}>
              {checkOfflineStatus()}
            </p>
          </div>

          <div className="card text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-800 mb-1">تطبيق PWA</h3>
            <p className="text-sm text-blue-600">يعمل بدون إنترنت</p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">الإجراءات السريعة</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              onClick={() => onNavigate && onNavigate('sales')}
              className="btn btn-primary flex-col h-20"
            >
              <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
              <span className="text-sm">بيع سريع</span>
            </button>

            <button
              onClick={() => onNavigate && onNavigate('products')}
              className="btn btn-success flex-col h-20"
            >
              <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
              <span className="text-sm">المنتجات</span>
            </button>

            <button
              onClick={() => onNavigate && onNavigate('customers')}
              className="btn btn-secondary flex-col h-20"
            >
              <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <span className="text-sm">الديون</span>
            </button>

            <button
              onClick={() => onNavigate && onNavigate('reports')}
              className="btn btn-secondary flex-col h-20"
            >
              <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <span className="text-sm">التقارير</span>
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>تطبيق إدارة المبيعات والمشتريات • الإصدار 1.0.0</p>
          <p className="mt-1">يعمل بدون إنترنت • تم التطوير بواسطة Augment Agent</p>
        </div>
      </div>
    </div>
  );
};

export default Welcome;
