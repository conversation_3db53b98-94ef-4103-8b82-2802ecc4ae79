<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#2563eb" />
    <meta name="description" content="تطبيق إدارة المبيعات والمشتريات للمحلات التجارية" />
    
    <!-- Google Fonts for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <title>نظام إدارة المبيعات والمشتريات</title>
    
    <style>
      :root {
        --primary-color: #2563eb;
        --primary-dark: #1d4ed8;
        --secondary-color: #64748b;
        --success-color: #059669;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --light-bg: #f8fafc;
        --white: #ffffff;
        --gray-100: #f1f5f9;
        --gray-200: #e2e8f0;
        --gray-300: #cbd5e1;
        --gray-400: #94a3b8;
        --gray-500: #64748b;
        --gray-600: #475569;
        --gray-700: #334155;
        --gray-800: #1e293b;
        --gray-900: #0f172a;
        --border-radius: 8px;
        --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      html {
        direction: rtl;
        font-size: 16px;
      }

      body {
        font-family: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        line-height: 1.6;
        color: var(--gray-800);
        background-color: var(--light-bg);
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
      }

      .card {
        background: var(--white);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        padding: 1.5rem;
        margin-bottom: 1rem;
      }

      .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1rem;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.2s ease;
        min-height: 44px;
      }

      .btn-primary {
        background-color: var(--primary-color);
        color: var(--white);
      }

      .btn-primary:hover {
        background-color: var(--primary-dark);
      }

      .btn-success {
        background-color: var(--success-color);
        color: var(--white);
      }

      .grid {
        display: grid;
      }

      .grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }

      .grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
      }

      .gap-4 {
        gap: 1rem;
      }

      .gap-6 {
        gap: 1.5rem;
      }

      .text-center {
        text-align: center;
      }

      .text-2xl {
        font-size: 1.5rem;
      }

      .text-3xl {
        font-size: 1.875rem;
      }

      .font-bold {
        font-weight: 700;
      }

      .font-semibold {
        font-weight: 600;
      }

      .mb-2 {
        margin-bottom: 0.5rem;
      }

      .mb-4 {
        margin-bottom: 1rem;
      }

      .mb-6 {
        margin-bottom: 1.5rem;
      }

      .mb-8 {
        margin-bottom: 2rem;
      }

      .py-8 {
        padding-top: 2rem;
        padding-bottom: 2rem;
      }

      .flex-col {
        flex-direction: column;
      }

      .h-20 {
        height: 5rem;
      }

      .w-6 {
        width: 1.5rem;
      }

      .h-6 {
        height: 1.5rem;
      }

      .ml-2 {
        margin-left: 0.5rem;
      }

      .mb-1 {
        margin-bottom: 0.25rem;
      }

      .text-sm {
        font-size: 0.875rem;
      }

      .bg-gradient-to-br {
        background-image: linear-gradient(to bottom right, var(--from), var(--to));
      }

      .from-blue-50 {
        --from: #eff6ff;
      }

      .to-indigo-100 {
        --to: #e0e7ff;
      }

      .min-h-screen {
        min-height: 100vh;
      }

      .w-20 {
        width: 5rem;
      }

      .h-20 {
        height: 5rem;
      }

      .w-10 {
        width: 2.5rem;
      }

      .h-10 {
        height: 2.5rem;
      }

      .rounded-full {
        border-radius: 9999px;
      }

      .rounded-lg {
        border-radius: 0.5rem;
      }

      .flex {
        display: flex;
      }

      .items-center {
        align-items: center;
      }

      .justify-center {
        justify-content: center;
      }

      .mx-auto {
        margin-left: auto;
        margin-right: auto;
      }

      .text-white {
        color: var(--white);
      }

      .text-gray-600 {
        color: var(--gray-600);
      }

      .text-gray-800 {
        color: var(--gray-800);
      }

      @media (max-width: 768px) {
        .grid-cols-4 {
          grid-template-columns: repeat(2, minmax(0, 1fr));
        }
      }
    </style>
  </head>
  <body>
    <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div class="container py-8">
        <!-- Header -->
        <div class="text-center mb-8">
          <div class="w-20 h-20 bg-primary-color rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </div>
          <h1 class="text-3xl font-bold text-gray-800 mb-2">
            متجر المواد الغذائية
          </h1>
          <p class="text-gray-600">
            نظام إدارة المبيعات والمشتريات - المرحلة الثانية
          </p>
        </div>

        <!-- Status Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="card text-center">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-800 mb-1">المرحلة الثانية</h3>
            <p class="text-sm text-green-600">مكتملة بنجاح</p>
          </div>

          <div class="card text-center">
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-800 mb-1">إدارة المنتجات</h3>
            <p class="text-sm text-blue-600">جاهزة للاستخدام</p>
          </div>

          <div class="card text-center">
            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-800 mb-1">تطبيق PWA</h3>
            <p class="text-sm text-purple-600">يعمل بدون إنترنت</p>
          </div>
        </div>

        <!-- Features -->
        <div class="card">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">الميزات المكتملة</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center">
              <svg class="w-6 h-6 text-green-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>نظام تنقل متقدم مع شريط علوي</span>
            </div>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-green-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>إدارة شاملة للمنتجات (CRUD)</span>
            </div>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-green-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>نموذج منتجات متطور مع التحقق</span>
            </div>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-green-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>قائمة تفاعلية مع بحث وفلترة</span>
            </div>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-green-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>تنبيهات المخزون المنخفض والمنتهي</span>
            </div>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-green-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>لوحة تحكم مع إحصائيات فورية</span>
            </div>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-green-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>حساب قيمة المخزون والأرباح</span>
            </div>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-green-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>تصميم متجاوب RTL</span>
            </div>
          </div>
        </div>

        <!-- Next Steps -->
        <div class="card">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">الخطوات التالية</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center">
              <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <h3 class="font-semibold text-gray-800 mb-2">المرحلة 3</h3>
              <p class="text-sm text-gray-600">شاشة المبيعات الفورية</p>
            </div>
            <div class="text-center">
              <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 class="font-semibold text-gray-800 mb-2">المرحلة 4</h3>
              <p class="text-sm text-gray-600">البيع بالآجل وإدارة الديون</p>
            </div>
            <div class="text-center">
              <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 class="font-semibold text-gray-800 mb-2">المرحلة 6</h3>
              <p class="text-sm text-gray-600">التقارير ولوحة المعلومات</p>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 text-sm text-gray-500">
          <p>تطبيق إدارة المبيعات والمشتريات • المرحلة 2 مكتملة</p>
          <p class="mt-1">تم التطوير بواسطة Augment Agent • يوليو 2025</p>
        </div>
      </div>
    </div>
  </body>
</html>
