import React from 'react';

const StockAlerts = ({ 
  lowStockProducts = [], 
  outOfStockProducts = [], 
  onUpdateStock,
  onEditProduct 
}) => {
  const totalAlerts = lowStockProducts.length + outOfStockProducts.length;

  if (totalAlerts === 0) {
    return (
      <div className="card">
        <div className="text-center py-8">
          <svg className="w-16 h-16 text-green-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد تنبيهات مخزون</h3>
          <p className="text-gray-500">جميع المنتجات متوفرة بكميات كافية</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Out of Stock */}
        <div className="card bg-red-50 border-red-200">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center ml-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-red-800">نفد المخزون</h3>
              <p className="text-red-600">{outOfStockProducts.length} منتج</p>
            </div>
          </div>
        </div>

        {/* Low Stock */}
        <div className="card bg-yellow-50 border-yellow-200">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center ml-4">
              <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800">مخزون منخفض</h3>
              <p className="text-yellow-600">{lowStockProducts.length} منتج</p>
            </div>
          </div>
        </div>
      </div>

      {/* Out of Stock Products */}
      {outOfStockProducts.length > 0 && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-xl font-semibold text-red-800">
              منتجات نفد مخزونها ({outOfStockProducts.length})
            </h3>
          </div>
          
          <div className="space-y-3">
            {outOfStockProducts.map((product) => (
              <div key={product.id} className="flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div>
                      <h4 className="font-medium text-gray-900">{product.name}</h4>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>الفئة: {product.category || 'عام'}</span>
                        <span>الوحدة: {product.unit}</span>
                        {product.barcode && <span>الباركود: {product.barcode}</span>}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                    نفد المخزون
                  </span>
                  
                  <div className="flex gap-1">
                    <button
                      onClick={() => onUpdateStock(product)}
                      className="btn btn-sm btn-primary"
                      title="تحديث المخزون"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </button>
                    
                    <button
                      onClick={() => onEditProduct(product)}
                      className="btn btn-sm btn-secondary"
                      title="تعديل المنتج"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Low Stock Products */}
      {lowStockProducts.length > 0 && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-xl font-semibold text-yellow-800">
              منتجات بمخزون منخفض ({lowStockProducts.length})
            </h3>
          </div>
          
          <div className="space-y-3">
            {lowStockProducts.map((product) => (
              <div key={product.id} className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div>
                      <h4 className="font-medium text-gray-900">{product.name}</h4>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>الفئة: {product.category || 'عام'}</span>
                        <span>الوحدة: {product.unit}</span>
                        {product.barcode && <span>الباركود: {product.barcode}</span>}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-sm text-gray-600">الكمية الحالية</div>
                    <div className="font-bold text-yellow-700">{product.stock}</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-sm text-gray-600">الحد الأدنى</div>
                    <div className="font-medium text-gray-700">{product.minStock}</div>
                  </div>
                  
                  <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
                    مخزون منخفض
                  </span>
                  
                  <div className="flex gap-1">
                    <button
                      onClick={() => onUpdateStock(product)}
                      className="btn btn-sm btn-primary"
                      title="تحديث المخزون"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </button>
                    
                    <button
                      onClick={() => onEditProduct(product)}
                      className="btn btn-sm btn-secondary"
                      title="تعديل المنتج"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="card bg-blue-50 border-blue-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-blue-800">إجراءات سريعة</h3>
            <p className="text-blue-600">قم بتحديث المخزون أو طلب منتجات جديدة</p>
          </div>
          
          <div className="flex gap-2">
            <button className="btn btn-primary">
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              تحديث مخزون متعدد
            </button>
            
            <button className="btn btn-secondary">
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              تصدير قائمة الطلب
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StockAlerts;
