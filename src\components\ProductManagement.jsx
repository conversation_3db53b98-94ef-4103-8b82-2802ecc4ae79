import React, { useState, useEffect } from 'react';
import { useProducts } from '../hooks/useProducts';
import ProductForm from './ProductForm';
import ProductList from './ProductList';
import StockAlerts from './StockAlerts';

const ProductManagement = () => {
  const {
    products,
    categories,
    loading,
    error,
    addProduct,
    updateProduct,
    deleteProduct,
    updateStock,
    getLowStockProducts,
    getOutOfStockProducts,
    getTotalInventoryValue,
    getPotentialProfit,
    clearError
  } = useProducts();

  const [currentView, setCurrentView] = useState('list'); // list, add, edit, alerts
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [productToDelete, setProductToDelete] = useState(null);
  const [showStockUpdate, setShowStockUpdate] = useState(false);
  const [productToUpdateStock, setProductToUpdateStock] = useState(null);
  const [newStockValue, setNewStockValue] = useState('');

  // Get alert data
  const lowStockProducts = getLowStockProducts();
  const outOfStockProducts = getOutOfStockProducts();
  const totalAlerts = lowStockProducts.length + outOfStockProducts.length;

  // Handle form submission
  const handleFormSubmit = async (formData) => {
    let result;
    
    if (selectedProduct) {
      result = await updateProduct(selectedProduct.id, formData);
    } else {
      result = await addProduct(formData);
    }

    if (result.success) {
      setCurrentView('list');
      setSelectedProduct(null);
    }
  };

  // Handle edit product
  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    setCurrentView('edit');
  };

  // Handle delete product
  const handleDeleteProduct = (product) => {
    setProductToDelete(product);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (productToDelete) {
      const result = await deleteProduct(productToDelete.id);
      if (result.success) {
        setShowDeleteConfirm(false);
        setProductToDelete(null);
      }
    }
  };

  // Handle stock update
  const handleUpdateStock = (product) => {
    setProductToUpdateStock(product);
    setNewStockValue(product.stock.toString());
    setShowStockUpdate(true);
  };

  const confirmStockUpdate = async () => {
    if (productToUpdateStock && newStockValue !== '') {
      const result = await updateStock(
        productToUpdateStock.id, 
        parseInt(newStockValue),
        'تحديث يدوي من واجهة إدارة المنتجات'
      );
      
      if (result.success) {
        setShowStockUpdate(false);
        setProductToUpdateStock(null);
        setNewStockValue('');
      }
    }
  };

  // Clear error when component unmounts or view changes
  useEffect(() => {
    return () => clearError();
  }, [currentView, clearError]);

  const renderHeader = () => (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">إدارة المنتجات</h1>
        <p className="text-gray-600">إضافة وتعديل وإدارة منتجات المتجر</p>
      </div>
      
      <div className="flex gap-2">
        <button
          onClick={() => setCurrentView('alerts')}
          className={`btn ${currentView === 'alerts' ? 'btn-primary' : 'btn-secondary'} relative`}
        >
          <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          التنبيهات
          {totalAlerts > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center">
              {totalAlerts}
            </span>
          )}
        </button>
        
        <button
          onClick={() => setCurrentView('list')}
          className={`btn ${currentView === 'list' ? 'btn-primary' : 'btn-secondary'}`}
        >
          <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
          </svg>
          قائمة المنتجات
        </button>
        
        <button
          onClick={() => {
            setSelectedProduct(null);
            setCurrentView('add');
          }}
          className={`btn ${currentView === 'add' ? 'btn-primary' : 'btn-success'}`}
        >
          <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          إضافة منتج
        </button>
      </div>
    </div>
  );

  const renderStats = () => (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div className="card text-center">
        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        </div>
        <h3 className="font-semibold text-gray-800 mb-1">إجمالي المنتجات</h3>
        <p className="text-2xl font-bold text-blue-600">{products.length}</p>
      </div>

      <div className="card text-center">
        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        </div>
        <h3 className="font-semibold text-gray-800 mb-1">قيمة المخزون</h3>
        <p className="text-2xl font-bold text-green-600">{getTotalInventoryValue().toFixed(2)} ريال</p>
      </div>

      <div className="card text-center">
        <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        </div>
        <h3 className="font-semibold text-gray-800 mb-1">الربح المتوقع</h3>
        <p className="text-2xl font-bold text-purple-600">{getPotentialProfit().toFixed(2)} ريال</p>
      </div>

      <div className="card text-center">
        <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <h3 className="font-semibold text-gray-800 mb-1">الفئات</h3>
        <p className="text-2xl font-bold text-orange-600">{categories.length}</p>
      </div>
    </div>
  );

  return (
    <div className="container py-6">
      {renderHeader()}
      
      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-red-800">{error}</span>
            <button
              onClick={clearError}
              className="mr-auto text-red-400 hover:text-red-600"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Stats */}
      {(currentView === 'list' || currentView === 'alerts') && renderStats()}

      {/* Content */}
      {currentView === 'list' && (
        <ProductList
          products={products}
          categories={categories}
          onEdit={handleEditProduct}
          onDelete={handleDeleteProduct}
          onUpdateStock={handleUpdateStock}
          loading={loading}
        />
      )}

      {(currentView === 'add' || currentView === 'edit') && (
        <ProductForm
          product={selectedProduct}
          categories={categories}
          onSubmit={handleFormSubmit}
          onCancel={() => {
            setCurrentView('list');
            setSelectedProduct(null);
          }}
          loading={loading}
        />
      )}

      {currentView === 'alerts' && (
        <StockAlerts
          lowStockProducts={lowStockProducts}
          outOfStockProducts={outOfStockProducts}
          onUpdateStock={handleUpdateStock}
          onEditProduct={handleEditProduct}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">تأكيد الحذف</h3>
            <p className="text-gray-600 mb-6">
              هل أنت متأكد من حذف المنتج "{productToDelete?.name}"؟ لا يمكن التراجع عن هذا الإجراء.
            </p>
            <div className="flex gap-4">
              <button
                onClick={confirmDelete}
                className="btn btn-danger flex-1"
              >
                حذف
              </button>
              <button
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setProductToDelete(null);
                }}
                className="btn btn-secondary flex-1"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Stock Update Modal */}
      {showStockUpdate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">تحديث المخزون</h3>
            <p className="text-gray-600 mb-4">
              المنتج: {productToUpdateStock?.name}
            </p>
            <div className="form-group mb-6">
              <label className="form-label">الكمية الجديدة</label>
              <input
                type="number"
                value={newStockValue}
                onChange={(e) => setNewStockValue(e.target.value)}
                className="form-input"
                placeholder="أدخل الكمية الجديدة"
                min="0"
                autoFocus
              />
            </div>
            <div className="flex gap-4">
              <button
                onClick={confirmStockUpdate}
                className="btn btn-primary flex-1"
                disabled={newStockValue === ''}
              >
                تحديث
              </button>
              <button
                onClick={() => {
                  setShowStockUpdate(false);
                  setProductToUpdateStock(null);
                  setNewStockValue('');
                }}
                className="btn btn-secondary flex-1"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductManagement;
