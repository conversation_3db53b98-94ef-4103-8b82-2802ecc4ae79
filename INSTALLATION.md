# دليل التثبيت والتشغيل

## متطلبات النظام

### البرامج المطلوبة:
- **Node.js** (الإصدار 18 أو أحدث)
- **npm** (يأتي مع Node.js)
- **Git** (اختياري للتحكم في الإصدارات)

### المتصفحات المدعومة:
- Chrome/Chromium 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## التثبيت

### 1. تحميل المشروع
```bash
# إذا كان لديك Git
git clone [repository-url]
cd grocery-pos

# أو تحميل الملفات مباشرة وفك الضغط
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. تشغيل التطبيق للتطوير
```bash
npm run dev
```

سيفتح التطبيق على: `http://localhost:5173`

### 4. بناء التطبيق للإنتاج
```bash
npm run build
```

### 5. معاينة النسخة المبنية
```bash
npm run preview
```

## طرق التشغيل البديلة

### استخدام خادم HTTP بسيط
```bash
# بناء التطبيق أولاً
npm run build

# تشغيل خادم HTTP
cd dist
python -m http.server 3000
# أو
npx serve -s . -p 3000
```

### استخدام Docker (اختياري)
```dockerfile
# Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "preview"]
```

```bash
docker build -t grocery-pos .
docker run -p 3000:3000 grocery-pos
```

## تثبيت PWA على الأجهزة

### على أندرويد (Chrome):
1. افتح التطبيق في Chrome
2. اضغط على قائمة المتصفح (⋮)
3. اختر "إضافة إلى الشاشة الرئيسية"
4. أكد التثبيت

### على iOS (Safari):
1. افتح التطبيق في Safari
2. اضغط على زر المشاركة (□↗)
3. اختر "إضافة إلى الشاشة الرئيسية"
4. أكد التثبيت

### على Windows/Mac (Chrome/Edge):
1. افتح التطبيق في المتصفح
2. ابحث عن أيقونة التثبيت في شريط العنوان
3. اضغط على "تثبيت"

## اختبار الميزات

### اختبار العمل بدون إنترنت:
1. افتح التطبيق في المتصفح
2. افتح أدوات المطور (F12)
3. اذهب إلى تبويب Network
4. اختر "Offline" من القائمة المنسدلة
5. أعد تحميل الصفحة - يجب أن تعمل

### اختبار قاعدة البيانات:
1. افتح أدوات المطور (F12)
2. اذهب إلى تبويب Application
3. في الشريط الجانبي، اختر Storage > IndexedDB
4. يجب أن ترى قاعدة بيانات "groceryPOS"
5. تحقق من وجود الجداول الستة

### اختبار التصميم المتجاوب:
1. افتح أدوات المطور (F12)
2. اضغط على أيقونة الجهاز المحمول
3. جرب أحجام شاشات مختلفة
4. تأكد من أن التطبيق يبدو جيداً على جميع الأحجام

## حل المشاكل الشائعة

### مشكلة: npm run dev لا يعمل
**الحل:**
```bash
# تأكد من أنك في مجلد المشروع الصحيح
pwd
ls package.json

# أعد تثبيت التبعيات
rm -rf node_modules package-lock.json
npm install

# جرب تشغيل vite مباشرة
npx vite
```

### مشكلة: الخطوط العربية لا تظهر
**الحل:**
- تأكد من الاتصال بالإنترنت لتحميل خطوط Google
- أو حمل الخطوط محلياً وضعها في مجلد `public/fonts`

### مشكلة: PWA لا يعمل بدون إنترنت
**الحل:**
- تأكد من أن Service Worker مسجل بنجاح
- تحقق من وجود ملف `sw.js` في مجلد `dist`
- امسح cache المتصفح وأعد تحميل الصفحة

### مشكلة: قاعدة البيانات لا تعمل
**الحل:**
```javascript
// افتح Console في المتصفح وجرب:
import { initDB } from './src/utils/db.js';
initDB().then(db => console.log('DB initialized:', db));
```

### مشكلة: التطبيق لا يفتح على المنفذ المطلوب
**الحل:**
```bash
# تحديد منفذ مخصص
npm run dev -- --port 3000

# أو تعديل vite.config.js
export default defineConfig({
  server: {
    port: 3000
  }
})
```

## إعدادات التطوير

### متغيرات البيئة (.env):
```env
VITE_APP_NAME=نظام إدارة المبيعات والمشتريات
VITE_APP_VERSION=1.0.0
VITE_DB_NAME=groceryPOS
```

### إعدادات VS Code الموصى بها:
```json
{
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  },
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

### ملحقات VS Code المفيدة:
- ES7+ React/Redux/React-Native snippets
- Auto Rename Tag
- Bracket Pair Colorizer
- Arabic Language Pack

## الأمان والخصوصية

### البيانات المحلية:
- جميع البيانات تُخزن محلياً في IndexedDB
- لا يتم إرسال أي بيانات إلى خوادم خارجية
- البيانات محمية بواسطة Same-Origin Policy

### النسخ الاحتياطي:
```javascript
// تصدير البيانات
import { exportData } from './src/utils/db.js';
const backup = await exportData();
console.log('Backup:', backup);

// حفظ النسخة الاحتياطية
const dataStr = JSON.stringify(backup);
const dataBlob = new Blob([dataStr], {type: 'application/json'});
const url = URL.createObjectURL(dataBlob);
const link = document.createElement('a');
link.href = url;
link.download = 'grocery-pos-backup.json';
link.click();
```

## الدعم والمساعدة

### الوثائق:
- [README.md](./README.md) - نظرة عامة على المشروع
- [CHANGELOG-PHASE-1.md](./CHANGELOG-PHASE-1.md) - سجل التغييرات

### المشاكل الشائعة:
- تحقق من Console في أدوات المطور للأخطاء
- تأكد من دعم المتصفح لـ IndexedDB و Service Workers
- تحقق من إعدادات الأمان في المتصفح

### معلومات التطوير:
- **المطور:** Augment Agent
- **الشركة:** Augment Code
- **الإصدار:** 1.0.0 (المرحلة 1)
- **التاريخ:** يوليو 2025

---

**🎉 مبروك! تم تثبيت التطبيق بنجاح**

الآن يمكنك البدء في استخدام نظام إدارة المبيعات والمشتريات!
